interface DateInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  max?: string;
  hint?: string;
  className?: string;
}

export const DateInput = ({
  label,
  value,
  onChange,
  error,
  required = false,
  max,
  hint,
  className = "",
}: DateInputProps) => {
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && "*"}
      </label>
      <input
        type="date"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
          error ? "border-red-300 bg-red-50" : "border-gray-300"
        }`}
        max={max}
      />
      {error && <p className="text-red-600 text-sm mt-1">{error}</p>}
      {hint && !error && <p className="text-gray-500 text-sm mt-1">{hint}</p>}
    </div>
  );
};