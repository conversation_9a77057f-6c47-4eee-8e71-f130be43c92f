import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import { toast } from "sonner";

const getAccountMethods = (isSoleProprietor: boolean) => 
  isSoleProprietor
    ? [
        { value: 20, label: "Personal Checking Account" },
        { value: 21, label: "Personal Savings Account" },
        { value: 10, label: "Corporate Checking Account" },
        { value: 11, label: "Corporate Savings Account" },
      ]
    : [
        { value: 10, label: "Corporate Checking Account" },
        { value: 11, label: "Corporate Savings Account" },
        { value: 20, label: "Personal Checking Account" },
        { value: 21, label: "Personal Savings Account" },
      ];

const formatDigits = (value: string, maxLength: number) => 
  value.replace(/\D/g, "").slice(0, maxLength);

const BankAccountForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [termsAccepted, setTermsAccepted] = useState(false);
  const isSoleProprietor = formData.type === 1;
  const account = formData.accounts?.[0] || {
    primary: 1,
    account: {
      method: isSoleProprietor ? 20 : 10,
      number: "",
      routing: "",
    },
  };
  const accountMethods = getAccountMethods(isSoleProprietor);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!account.account.routing?.trim()) newErrors.routing = "Routing number is required";
    if (!account.account.number?.trim()) newErrors.number = "Account number is required";
    if (account.account.routing && !/^\d{9}$/.test(account.account.routing.replace(/\D/g, ""))) {
      newErrors.routing = "Routing number must be 9 digits";
    }
    if (account.account.number && !/^\d{4,17}$/.test(account.account.number.replace(/\D/g, ""))) {
      newErrors.number = "Account number must be between 4-17 digits";
    }
    if (!termsAccepted) newErrors.terms = "You must acknowledge and agree to the terms and conditions";
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) {
      toast.error("Please fix the following errors:", {
        description: Object.values(newErrors).join(", "),
      });
      return false;
    }
    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      dispatch(nextStep());
    }
  };

  const handleChange = (field: string, value: string | number) => {
    const updatedAccount = field.startsWith("account.")
      ? {
          ...account,
          currency: "USD",
          account: { ...account.account, [field.replace("account.", "")]: value },
        }
      : { ...account, currency: "USD", [field]: value };
    dispatch(updateFormData({ accounts: [updatedAccount] }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Bank Account Information</h1>
            <p className="text-gray-600 mt-1">Enter your business bank account details for payment processing</p>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Account Details</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Account Type *</label>
                  <select
                    value={account.account.method || ""}
                    onChange={(e) => handleChange("account.method", parseInt(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  >
                    <option value="">Select account type</option>
                    {accountMethods.map((method) => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                  <p className="text-gray-500 text-sm mt-1">
                    {isSoleProprietor
                      ? "Personal accounts are typically used for sole proprietorships, but corporate accounts are also available"
                      : "Corporate accounts are recommended for business entities"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Routing Number *</label>
                  <input
                    type="text"
                    value={account.account.routing || ""}
                    onChange={(e) => handleChange("account.routing", formatDigits(e.target.value, 9))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.routing ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="*********"
                    maxLength={9}
                  />
                  {errors.routing && <p className="text-red-600 text-sm mt-1">{errors.routing}</p>}
                  <p className="text-gray-500 text-sm mt-1">9-digit number found on the bottom of your checks</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Account Number *</label>
                  <input
                    type="text"
                    value={account.account.number || ""}
                    onChange={(e) => handleChange("account.number", formatDigits(e.target.value, 17))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.number ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="****************"
                    maxLength={17}
                  />
                  {errors.number && <p className="text-red-600 text-sm mt-1">{errors.number}</p>}
                  <p className="text-gray-500 text-sm mt-1">Account number found on your checks or bank statements</p>
                </div>
              </div>
            </div>

            <div className="mb-10">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-1">Important Information</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p>• This account will be used for payment processing deposits</p>
                      <p>• Ensure the account is active and in good standing</p>
                      <p>• The account holder name should match your business name</p>
                      <p>• Business accounts are preferred for business transactions</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-10">
              <div className={`bg-gray-50 border rounded-lg p-6 ${errors.terms ? "border-red-300 bg-red-50" : "border-gray-200"}`}>
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="terms"
                    checked={termsAccepted}
                    onChange={(e) => setTermsAccepted(e.target.checked)}
                    className={`w-4 h-4 border rounded focus:ring-blue-500 mt-0.5 ${
                      errors.terms ? "border-red-300 text-red-600 focus:ring-red-500" : "border-gray-300 text-blue-600"
                    }`}
                  />
                  <label htmlFor="terms" className="text-sm text-gray-700">
                    <span className="font-medium">I acknowledge and agree</span> that the bank account information provided is accurate and that I
                    have authorization to use this account for payment processing. I understand that providing false information may result in account
                    suspension or termination.
                  </label>
                </div>
                {errors.terms && <p className="text-red-600 text-sm mt-2 ml-7">{errors.terms}</p>}
              </div>
            </div>

            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Review & Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BankAccountForm;
