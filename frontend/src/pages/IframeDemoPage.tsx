import { useState, useRef } from "react";
import { PageLayout } from "../components/PageLayout";
import { ContentCard } from "../components/ContentCard";
import { ConfigurationForm, PaymentIframePreview, EventLog, IntegrationUrl } from "./iframe-demo/components";
import { useIframeEvents, useTokenGeneration } from "./iframe-demo/hooks";
import { DEFAULT_DEMO_CONFIG, DEMO_SECTIONS } from "./iframe-demo/constants";
import { DemoConfig } from "./iframe-demo/types";

const IframeDemoPage = () => {
  const [config, setConfig] = useState<DemoConfig>(DEFAULT_DEMO_CONFIG);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  const { embedUrl, loading, generateToken } = useTokenGeneration();
  const { events, iframeLoaded, clearEvents, resetIframeState } = useIframeEvents();

  const handleGenerateToken = async () => {
    resetIframeState();
    await generateToken(config);
  };

  return (
    <PageLayout
      title="Live Iframe Integration Demo"
      subtitle="Test the complete iframe payment integration flow in real-time"
      sections={DEMO_SECTIONS}
      gradientFrom="from-slate-800"
      gradientTo="to-slate-900"
    >
      <ContentCard id="configuration" title="Payment Configuration" variant="highlight">
        <ConfigurationForm
          config={config}
          setConfig={setConfig}
          onGenerateToken={handleGenerateToken}
          loading={loading}
        />
      </ContentCard>

      <ContentCard id="iframe-demo" title="Payment Iframe">
        <PaymentIframePreview
          embedUrl={embedUrl}
          iframeRef={iframeRef}
          iframeLoaded={iframeLoaded}
        />
      </ContentCard>

      <ContentCard id="event-log" title="Event Log">
        <EventLog events={events} onClearEvents={clearEvents} />
      </ContentCard>

      {embedUrl && (
        <ContentCard id="integration-url" title="Integration URL" variant="highlight">
          <IntegrationUrl embedUrl={embedUrl} />
        </ContentCard>
      )}
    </PageLayout>
  );
};

export default IframeDemoPage;