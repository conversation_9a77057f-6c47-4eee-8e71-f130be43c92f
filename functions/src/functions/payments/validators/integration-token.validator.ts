import { IntegrationTokenRequest } from "../../../types/integration-token.types.js";
import {
  validateMerchantIdFormat,
  validateDescription,
  validateAmount,
  validateReturnUrl,
} from "../../../middleware/security.js";

export interface ValidationResult {
  isValid: boolean;
  statusCode?: number;
  error?: string;
  message?: string;
  details?: any;
}

export function validateTaxAmount(taxAmount: number | undefined, amount: number): ValidationResult {
  if (taxAmount !== undefined && taxAmount > 0) {
    const taxPercentage = (taxAmount / amount) * 100;
    if (taxPercentage < 0.1 || taxPercentage > 30) {
      return {
        isValid: false,
        statusCode: 400,
        error: "Invalid tax amount",
        message: "Tax amount must be between 0.1% and 30% of the total amount",
        details: {
          taxAmount,
          amount,
          percentage: taxPercentage.toFixed(2),
        },
      };
    }
  }
  return { isValid: true };
}

export function validateItemizedTransaction(request: IntegrationTokenRequest): ValidationResult {
  if (request.items && request.items.length > 0) {
    if (!request.orderNumber && !request.invoiceNumber && !request.customerCode) {
      return {
        isValid: false,
        statusCode: 400,
        error: "Missing required field",
        message: "Order number, invoice number, or customer code is required for itemized transactions",
      };
    }
  }
  return { isValid: true };
}

export function validateIntegrationTokenRequest(request: IntegrationTokenRequest): ValidationResult[] {
  const validations: ValidationResult[] = [];

  const merchantValidation = validateMerchantIdFormat(request.merchantId);
  if (!merchantValidation.isValid) {
    validations.push({
      isValid: false,
      statusCode: merchantValidation.statusCode || 400,
      error: merchantValidation.error,
      message: "Invalid merchant ID",
    });
  }

  const descriptionValidation = validateDescription(request.description);
  if (!descriptionValidation.isValid) {
    validations.push({
      isValid: false,
      statusCode: descriptionValidation.statusCode || 400,
      error: descriptionValidation.error,
      message: "Invalid description",
    });
  }

  const amountValidation = validateAmount(request.amount || 1000);
  if (!amountValidation.isValid) {
    validations.push({
      isValid: false,
      statusCode: amountValidation.statusCode || 400,
      error: amountValidation.error,
      message: "Invalid amount",
    });
  }

  if (request.returnUrl) {
    const urlValidation = validateReturnUrl(request.returnUrl);
    if (!urlValidation.isValid) {
      validations.push({
        isValid: false,
        statusCode: urlValidation.statusCode || 400,
        error: urlValidation.error,
        message: "Invalid return URL",
      });
    }
  }

  const taxValidation = validateTaxAmount(request.taxAmount, request.amount || 1000);
  if (!taxValidation.isValid) {
    validations.push(taxValidation);
  }

  const itemValidation = validateItemizedTransaction(request);
  if (!itemValidation.isValid) {
    validations.push(itemValidation);
  }

  return validations;
}